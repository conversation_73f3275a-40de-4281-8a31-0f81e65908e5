import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import DetailedResourceView from '../DetailedResourceView';
import { useBookmarkContext } from '@/contexts/BookmarkContext';
import { useAuth } from '@/contexts/AuthContext';
import { useShare } from '@/hooks/useShare';
import { Entity } from '@/types/entity';

// Mock the hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@/contexts/BookmarkContext', () => ({
  useBookmarkContext: jest.fn(),
}));

jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

jest.mock('@/hooks/useShare', () => ({
  useShare: jest.fn(),
}));

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: any) {
    return <img src={src} alt={alt} {...props} />;
  };
});

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ href, children, ...props }: any) {
    return <a href={href} {...props}>{children}</a>;
  };
});

const mockEntity: Entity = {
  id: 'test-entity-1',
  slug: 'test-entity',
  name: 'Test Entity',
  shortDescription: 'A test entity for testing purposes',
  description: 'A longer description of the test entity',
  logoUrl: 'https://example.com/logo.png',
  websiteUrl: 'https://example.com',
  avgRating: 4.5,
  reviewCount: 10,
  entityType: {
    id: 'type-1',
    name: 'AI Tool',
    description: 'AI Tool type',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  categories: [
    {
      id: 'cat-1',
      name: 'Machine Learning',
      description: 'ML category',
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  ],
  features: [
    {
      id: 'feat-1',
      name: 'Feature 1',
      description: 'First feature',
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  ],
  tags: [
    {
      id: 'tag-1',
      name: 'Tag 1',
      description: 'First tag',
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  ],
  hasFreeTier: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

describe('DetailedResourceView', () => {
  const mockPush = jest.fn();
  const mockToggleBookmark = jest.fn();
  const mockIsBookmarked = jest.fn();
  const mockShareEntity = jest.fn();
  const mockOnLoadMoreReviews = jest.fn();
  const mockOnSubmitReview = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });

    (useBookmarkContext as jest.Mock).mockReturnValue({
      isBookmarked: mockIsBookmarked,
      toggleBookmark: mockToggleBookmark,
    });

    (useAuth as jest.Mock).mockReturnValue({
      session: { access_token: 'test-token' },
    });

    (useShare as jest.Mock).mockReturnValue({
      shareEntity: mockShareEntity,
    });

    mockIsBookmarked.mockReturnValue(false);
  });

  it('renders bookmark button with correct initial state', () => {
    render(
      <DetailedResourceView
        entity={mockEntity}
        reviews={[]}
        onLoadMoreReviews={mockOnLoadMoreReviews}
        hasMoreReviews={false}
        isLoadingReviews={false}
        reviewsTotalCount={0}
        onSubmitReview={mockOnSubmitReview}
        isSubmittingReview={false}
        reviewSubmissionError={null}
        reviewSubmissionSuccess={false}
      />
    );

    const bookmarkButton = screen.getByText('Save');
    expect(bookmarkButton).toBeInTheDocument();
  });

  it('shows "Saved" when entity is bookmarked', () => {
    mockIsBookmarked.mockReturnValue(true);
    
    render(
      <DetailedResourceView
        entity={mockEntity}
        reviews={[]}
        onLoadMoreReviews={mockOnLoadMoreReviews}
        hasMoreReviews={false}
        isLoadingReviews={false}
        reviewsTotalCount={0}
        onSubmitReview={mockOnSubmitReview}
        isSubmittingReview={false}
        reviewSubmissionError={null}
        reviewSubmissionSuccess={false}
      />
    );

    const bookmarkButton = screen.getByText('Saved');
    expect(bookmarkButton).toBeInTheDocument();
  });

  it('calls toggleBookmark when bookmark button is clicked', async () => {
    render(
      <DetailedResourceView
        entity={mockEntity}
        reviews={[]}
        onLoadMoreReviews={mockOnLoadMoreReviews}
        hasMoreReviews={false}
        isLoadingReviews={false}
        reviewsTotalCount={0}
        onSubmitReview={mockOnSubmitReview}
        isSubmittingReview={false}
        reviewSubmissionError={null}
        reviewSubmissionSuccess={false}
      />
    );

    const bookmarkButton = screen.getByText('Save');
    fireEvent.click(bookmarkButton);

    await waitFor(() => {
      expect(mockToggleBookmark).toHaveBeenCalledWith('test-entity-1');
    });
  });

  it('redirects to login when bookmark is clicked without authentication', async () => {
    (useAuth as jest.Mock).mockReturnValue({
      session: null,
    });

    render(
      <DetailedResourceView
        entity={mockEntity}
        reviews={[]}
        onLoadMoreReviews={mockOnLoadMoreReviews}
        hasMoreReviews={false}
        isLoadingReviews={false}
        reviewsTotalCount={0}
        onSubmitReview={mockOnSubmitReview}
        isSubmittingReview={false}
        reviewSubmissionError={null}
        reviewSubmissionSuccess={false}
      />
    );

    const bookmarkButton = screen.getByText('Save');
    fireEvent.click(bookmarkButton);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/login');
    });
  });

  it('calls shareEntity when share button is clicked', async () => {
    render(
      <DetailedResourceView
        entity={mockEntity}
        reviews={[]}
        onLoadMoreReviews={mockOnLoadMoreReviews}
        hasMoreReviews={false}
        isLoadingReviews={false}
        reviewsTotalCount={0}
        onSubmitReview={mockOnSubmitReview}
        isSubmittingReview={false}
        reviewSubmissionError={null}
        reviewSubmissionSuccess={false}
      />
    );

    const shareButton = screen.getByText('Share');
    fireEvent.click(shareButton);

    await waitFor(() => {
      expect(mockShareEntity).toHaveBeenCalledWith(mockEntity);
    });
  });

  it('handles bookmark loading state', async () => {
    mockToggleBookmark.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(
      <DetailedResourceView
        entity={mockEntity}
        reviews={[]}
        onLoadMoreReviews={mockOnLoadMoreReviews}
        hasMoreReviews={false}
        isLoadingReviews={false}
        reviewsTotalCount={0}
        onSubmitReview={mockOnSubmitReview}
        isSubmittingReview={false}
        reviewSubmissionError={null}
        reviewSubmissionSuccess={false}
      />
    );

    const bookmarkButton = screen.getByText('Save');
    fireEvent.click(bookmarkButton);

    // Should show loading state
    expect(bookmarkButton).toBeDisabled();
    
    await waitFor(() => {
      expect(bookmarkButton).not.toBeDisabled();
    });
  });
});
