'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, AlertCircle } from 'lucide-react';
import { RecommendationResponse } from '@/types/recommendation';
import ResourceCard from '@/components/resource/ResourceCard';
import { CardLoading } from '@/components/loading/LoadingStates';

interface RecommendationDisplayProps {
  recommendation: RecommendationResponse | null;
  isLoading: boolean;
  error: string | null;
}

const RecommendationDisplay: React.FC<RecommendationDisplayProps> = ({
  recommendation,
  isLoading,
  error,
}) => {
  // Don't render anything if there's no content to show
  if (!isLoading && !error && !recommendation) {
    return null;
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Loading explanation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-indigo-600" />
              AI Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
            </div>
          </CardContent>
        </Card>

        {/* Loading cards */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Recommended Tools & Resources
          </h3>
          <CardLoading count={3} />
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-red-900 mb-1">
                Unable to get recommendations
              </h3>
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Success state with recommendations
  if (recommendation) {
    return (
      <div className="space-y-6">
        {/* AI Explanation */}
        {recommendation.explanation && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-indigo-600" />
                AI Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose prose-sm max-w-none">
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {recommendation.explanation}
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recommended Entities */}
        {recommendation.recommendedEntities && recommendation.recommendedEntities.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Recommended Tools & Resources
              </h3>
              <Badge variant="secondary" className="text-xs">
                {recommendation.recommendedEntities.length} {recommendation.recommendedEntities.length === 1 ? 'result' : 'results'}
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {recommendation.recommendedEntities.map((entity) => (
                <ResourceCard
                  key={entity.id}
                  entity={entity}
                />
              ))}
            </div>
          </div>
        )}

        {/* No results message */}
        {recommendation.recommendedEntities && recommendation.recommendedEntities.length === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No specific tools found
                </h3>
                <p className="text-gray-600 max-w-md mx-auto">
                  I couldn't find specific tools matching your description, but the analysis above might still be helpful. 
                  Try refining your search or browse our catalog for similar tools.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  return null;
};

export default RecommendationDisplay;
