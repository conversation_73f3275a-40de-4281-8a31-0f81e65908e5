'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { MessageCircle, Send, Sparkles, LogIn, UserPlus } from 'lucide-react';
import { getRecommendations } from '@/services/api';
import { RecommendationResponse, RecommendationRequest } from '@/types/recommendation';
import { useAuth } from '@/contexts/AuthContext';
import RecommendationDisplay from './RecommendationDisplay';

interface ChatbotProps {
  className?: string;
}

const Chatbot: React.FC<ChatbotProps> = ({ className = '' }) => {
  // State management
  const [userInput, setUserInput] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [recommendation, setRecommendation] = useState<RecommendationResponse | null>(null);
  const [showLoginPrompt, setShowLoginPrompt] = useState<boolean>(false);

  // Auth context for potential token usage
  const { session, user } = useAuth();

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate input
    if (!userInput.trim()) {
      setError('Please describe what you\'re looking for.');
      return;
    }

    // Check if user is authenticated
    if (!user || !session?.access_token) {
      setShowLoginPrompt(true);
      setError(null);
      setRecommendation(null);
      return;
    }

    // Reset states
    setIsLoading(true);
    setError(null);
    setRecommendation(null);
    setShowLoginPrompt(false);

    try {
      // Prepare request payload
      const payload: RecommendationRequest = {
        problem_description: userInput.trim(),
        // Optional: Add filters in the future
        filters: {}
      };

      // Call API
      const response = await getRecommendations(payload, session.access_token);
      setRecommendation(response);
    } catch (err) {
      console.error('Error getting recommendations:', err);
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to get recommendations. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setUserInput(e.target.value);
    // Clear error and login prompt when user starts typing
    if (error) {
      setError(null);
    }
    if (showLoginPrompt) {
      setShowLoginPrompt(false);
    }
  };

  // Handle clear/reset
  const handleClear = () => {
    setUserInput('');
    setError(null);
    setRecommendation(null);
    setShowLoginPrompt(false);
  };

  return (
    <div className={`w-full max-w-4xl mx-auto space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2 mb-4">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-indigo-600">
            <Sparkles className="h-6 w-6 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900">AI Assistant</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Describe what you're looking for and I'll recommend the best AI tools and resources for your needs.
          {!user && (
            <span className="block mt-2 text-sm text-indigo-600">
              Sign in to get personalized recommendations.
            </span>
          )}
        </p>
      </div>

      {/* Input Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5 text-indigo-600" />
            What can I help you find?
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="problem-description">
                Describe your problem or what you're looking for
              </Label>
              <Textarea
                id="problem-description"
                placeholder="e.g., I need an AI tool to help me generate images for my marketing campaigns..."
                value={userInput}
                onChange={handleInputChange}
                className="min-h-[120px] resize-none"
                disabled={isLoading}
              />
            </div>

            {/* Error display */}
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Login prompt */}
            {showLoginPrompt && (
              <div className="p-4 bg-indigo-50 border border-indigo-200 rounded-md">
                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-indigo-100 flex-shrink-0">
                    <LogIn className="h-4 w-4 text-indigo-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-indigo-900 mb-1">
                      Sign in required
                    </h3>
                    <p className="text-sm text-indigo-700 mb-3">
                      Please sign in to get personalized AI recommendations for your needs.
                    </p>
                    <div className="flex gap-2">
                      <Button asChild size="sm" className="text-xs">
                        <Link href="/login">
                          <LogIn className="mr-1 h-3 w-3" />
                          Sign In
                        </Link>
                      </Button>
                      <Button asChild variant="outline" size="sm" className="text-xs">
                        <Link href="/register">
                          <UserPlus className="mr-1 h-3 w-3" />
                          Create Account
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Action buttons */}
            <div className="flex gap-3 justify-end">
              {(userInput || recommendation) && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClear}
                  disabled={isLoading}
                >
                  Clear
                </Button>
              )}
              <Button
                type="submit"
                disabled={isLoading || !userInput.trim()}
                className="min-w-[120px]"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Searching...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Send className="h-4 w-4" />
                    Get Recommendations
                  </div>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Results Display */}
      {!showLoginPrompt && (
        <RecommendationDisplay
          recommendation={recommendation}
          isLoading={isLoading}
          error={error}
        />
      )}
    </div>
  );
};

export default Chatbot;
